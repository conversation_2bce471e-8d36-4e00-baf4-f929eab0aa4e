import { useState } from "react"
import Common from "../Common"
import "./index.css"

interface SSOLoginProps {
  onBack: () => void
}

function SSOLogin({ onBack }: SSOLoginProps) {
  const [companyCode, setCompanyCode] = useState("")
  const handleVerify = () => {
    if (!companyCode.trim()) {
      alert("请输入企业代码或专属访问域名")
      return
    }
    console.log("验证企业代码:", companyCode)
    alert("验证成功")
  }
  return (
    <Common
      isShowBack={true}
      onBack={onBack}
      title="第三方企业登录"
      subTitle="使用金山办公在线服务账号登录"
      fchildren={
        <div className="login_footer">
          <p>获取企业代码和专属访问域名请联系您的企业管理员</p>
        </div>
      }
    >
      <div className="sso_login_section">
        <div className="sso_input_group">
          <input
            type="text"
            className="sso_input"
            placeholder="输入企业代码或专属访问域名"
            value={companyCode}
            onChange={(e) => setCompanyCode(e.target.value)}
          />
        </div>

        <button className="verify_btn" onClick={handleVerify}>
          验证
        </button>
      </div>
    </Common>
  )
}

export default SSOLogin
