import Common from "../Common"
import "./index.css"
interface SSOLoginProps {
  onBack: () => void
}

function SSOLogin({ onBack }: SSOLoginProps) {
  return (
    <div className="login">
      <button className="back_btn" onClick={onBack}>
        ← 返回
      </button>
      <Common
        title="第三方企业登录"
        subTitle="使用金山办公在线服务账号登录"
        fchildren={
          <div className="login_footer">
            <p>获取企业代码和专属访问域名请联系您的企业管理员</p>
          </div>
        }
      >
        <div className="sso_input">
          <input type="text" placeholder="请输入企业代码或专属访问域名" />
        </div>
        <button className="login_submit_btn">验证</button>
      </Common>
    </div>
  )
}

export default SSOLogin
