import { useState } from "react"
import ExCompany from "../../components/ExCompany"
import Modal from "../../components/Modal"
import PhoneLogin from "../../components/PhoneLogin"
import QRCodeLogin from "../../components/QRCodeLogin"
import SSOLogin from "../../components/SSOLogin"
import "./index.css"

function Home() {
  const [showModal, setShowModal] = useState(false)
  const [currentBtn, setCurrentBtn] = useState<"phone" | "sso" | "more" | null>(
    null
  )
  const [currentView, setCurrentView] = useState<
    "qrcode" | "phone" | "sso" | "more"
  >("qrcode")

  const handlePhoneClick = () => {
    setShowModal(true)
  }

  const handleSSOClick = () => {
    setCurrentView("sso")
  }

  const handleCurrentBtn = (value: "phone" | "sso" | "more") => {
    setCurrentBtn(value)
    setShowModal(true)
  }

  const handleMoreClick = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
    setCurrentBtn(null)
  }

  //通过记录的点击按钮类型来判断confirm后渲染哪一个组件
  const handleConfirm = () => {
    setShowModal(false)
    if (currentBtn) {
      setCurrentView(currentBtn)
    }
    setCurrentBtn(null)
  }

  const handleBackToQRCode = () => {
    setCurrentView("qrcode")
  }

  const handleBackFromSSO = () => {
    setCurrentView("qrcode")
  }

  const handleBackFromExCompany = () => {
    setCurrentView("qrcode")
  }

  return (
    <div className="wrap">
      <div className="left_section">
        <img
          src="https://ac.wpscdn.cn/account/libs/img/v2/logo/logo_x2.89795d69.png"
          alt="WPS Logo"
          className="logo_image"
        />
      </div>

      <div className="right_section">
        <div className="login_container">
          {currentView === "qrcode" && (
            <QRCodeLogin
              onPhoneClick={handlePhoneClick}
              onSSOClick={handleSSOClick}
              onMoreClick={handleMoreClick}
              onCurrentBtn={handleCurrentBtn}
            />
          )}
          {currentView === "phone" && (
            <PhoneLogin onBack={handleBackToQRCode} />
          )}
          {currentView === "sso" && <SSOLogin onBack={handleBackFromSSO} />}

          {currentView === "more" && (
            <ExCompany onBack={handleBackFromExCompany} />
          )}
        </div>
      </div>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        onConfirm={handleConfirm}
      />
    </div>
  )
}

export default Home
