import { useState } from "react"
import Modal from "../../components/Modal"
import PhoneLogin from "../../components/PhoneLogin"
import QRCodeLogin from "../../components/QRCodeLogin"
import SSOLogin from "../../components/SSOLogin"
import "./index.css"

function Home() {
  const [showModal, setShowModal] = useState(false)
  const [currentView, setCurrentView] = useState<"qrcode" | "phone" | "sso">(
    "qrcode"
  )

  const handlePhoneClick = () => {
    setShowModal(true)
  }

  const handleSSOClick = () => {
    setCurrentView("sso")
  }
  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleConfirm = () => {
    setShowModal(false)
    setCurrentView("phone")
  }

  const handleBackToQRCode = () => {
    setCurrentView("qrcode")
  }

  const handleBackFromSSO = () => {
    setCurrentView("qrcode")
  }

  return (
    <div className="wrap">
      <div className="left_section">
        <img
          src="https://ac.wpscdn.cn/account/libs/img/v2/logo/logo_x2.89795d69.png"
          alt="WPS Logo"
          className="logo_image"
        />
      </div>

      <div className="right_section">
        <div className="login_container">
          {/* {currentView === "qrcode" ? (
            <QRCodeLogin onPhoneClick={handlePhoneClick} />
          ) : (
            <PhoneLogin onBack={handleBackToQRCode} />
          )} */}

          {currentView === "qrcode" && (
            <QRCodeLogin
              onPhoneClick={handlePhoneClick}
              onSSOClick={handleSSOClick}
            />
          )}
          {currentView === "phone" && (
            <PhoneLogin onBack={handleBackToQRCode} />
          )}
          {currentView === "sso" && <SSOLogin onBack={handleBackFromSSO} />}
        </div>
      </div>

      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        onConfirm={handleConfirm}
      />
    </div>
  )
}

export default Home
