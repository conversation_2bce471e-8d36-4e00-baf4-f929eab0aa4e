import { useState } from "react"
import "./index.css"

function Home() {
  const [showModal, setShowModal] = useState(false)
  const [currentView, setCurrentView] = useState<"qrcode" | "phone">("qrcode")

  const handlePhoneClick = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  const handleConfirm = () => {
    setShowModal(false)
    setCurrentView("phone")
  }

  const handleBackToQRCode = () => {
    setCurrentView("qrcode")
  }

  return (
    <div className="wrap">
      <div className="left_section">
        <div className="logo_wrap">
          <img
            src="https://ac.wpscdn.cn/account/libs/img/v2/logo/logo_x2.89795d69.png"
            alt="金山办公 KINGSOFT OFFICE 释放智慧的力量 Inspire Wisdom."
            className="logo_image"
          />
        </div>
      </div>

      <div className="right_section">
        <div className="login_container">
          {currentView === "qrcode" ? (
            // 微信扫码登录界面
            <>
              <div className="login_header">
                <div className="nav_title">微信扫码登录</div>
                <div className="nav_intro">使用金山办公在线服务账号登录</div>
              </div>

              <div className="qr_code_section">
                <div className="qr_code_container">
                  <div className="qr_code_placeholder"></div>
                </div>
              </div>

              <div className="login_options">
                <div className="checkbox_section">
                  <div className="form_checkbox">
                    <input type="checkbox" id="keepOnline" />
                    <label htmlFor="keepOnline">
                      <span className="checkbox_icon"></span>
                      <span className="checkbox_text">自动登录</span>
                    </label>
                  </div>
                  <div className="form_checkbox checked">
                    <input type="checkbox" id="loginProtocal" defaultChecked />
                    <label htmlFor="loginProtocal">
                      <span className="checkbox_icon"></span>
                      <span className="checkbox_text">已阅读并同意</span>
                      <a
                        href="https://privacy.wps.cn/policies/privacy/kdocs"
                        target="_blank"
                        className="protocol_link"
                      >
                        隐私政策
                      </a>
                      <span>和</span>
                      <a
                        href="https://www.kdocs.cn/m/privacy"
                        target="_blank"
                        className="protocol_link"
                      >
                        在线服务协议
                      </a>
                    </label>
                  </div>
                </div>

                <div className="login_methods">
                  <div className="login_method">
                    <div className="method_icon qq_icon"></div>
                    <span className="method_text">QQ账号</span>
                  </div>
                  <div className="login_method" onClick={handlePhoneClick}>
                    <div className="method_icon phone_icon"></div>
                    <span className="method_text">手机</span>
                  </div>
                  <div className="login_method">
                    <div className="method_icon sso_icon"></div>
                    <span className="method_text">专属账号</span>
                  </div>
                  <div className="login_method">
                    <div className="method_icon more_icon"></div>
                    <span className="method_text">更多</span>
                  </div>
                </div>
              </div>
            </>
          ) : (
            // 手机验证码登录界面
            <>
              <div className="login_header">
                <button className="back_btn" onClick={handleBackToQRCode}>
                  ← 返回
                </button>
                <div className="nav_title">短信验证码登录</div>
                <div className="nav_intro">使用金山办公在线服务账号登录</div>
              </div>

              <div className="phone_login_section">
                <div className="phone_input_group">
                  <div className="country_code_select">
                    <select className="country_code">
                      <option value="+86">+86</option>
                    </select>
                  </div>
                  <input
                    type="tel"
                    className="phone_input"
                    placeholder="手机号码"
                  />
                </div>

                <div className="verification_group">
                  <input
                    type="text"
                    className="verification_input"
                    placeholder="验证码"
                  />
                  <button className="send_code_btn">获取验证码/手机</button>
                </div>

                <div className="verification_tip">
                  <span className="tip_icon">✓</span>
                  <span className="tip_text">验证码将发送到您的手机号码</span>
                </div>

                <button className="login_submit_btn">立即登录/注册</button>

                <div className="phone_login_footer">
                  <p>未注册的手机号验证后将自动创建金山账号</p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* 弹窗 */}
      {showModal && (
        <div className="modal_overlay" onClick={handleCloseModal}>
          <div className="modal_content" onClick={(e) => e.stopPropagation()}>
            <div className="modal_header">
              <span className="modal_title">提示</span>
              <button className="modal_close" onClick={handleCloseModal}>
                ×
              </button>
            </div>
            <div className="modal_body">
              <p>
                此功能暂未开放，请使用微信扫码登录。点击下方"确定"按钮，或者点击"取消"按钮返回。
              </p>
            </div>
            <div className="modal_footer">
              <button
                className="modal_btn cancel_btn"
                onClick={handleCloseModal}
              >
                取消
              </button>
              <button className="modal_btn confirm_btn" onClick={handleConfirm}>
                确定
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Home
