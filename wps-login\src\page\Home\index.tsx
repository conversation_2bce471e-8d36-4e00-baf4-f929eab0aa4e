import "./index.css"

function Home() {
  return (
    <div className="wrap">
      <div className="left_section">
        <div className="logo_wrap">
          <div className="logo"></div>
          <div className="logo_text">
            <div className="company_name">金山办公</div>
            <div className="company_name_en">KINGSOFT OFFICE</div>
            <div className="slogan">释放智慧的力量</div>
            <div className="slogan_en">Inspire Wisdom.</div>
          </div>
        </div>
      </div>

      <div className="right_section">
        <div className="login_container">
          <div className="login_header">
            <div className="nav_title">微信扫码登录</div>
            <div className="nav_intro">使用金山办公在线服务账号登录</div>
          </div>

          <div className="qr_code_section">
            <div className="qr_code_container">
              <div className="qr_code_placeholder"></div>
            </div>
          </div>

          <div className="login_options">
            <div className="checkbox_section">
              <div className="form_checkbox">
                <input type="checkbox" id="keepOnline" />
                <label htmlFor="keepOnline">
                  <span className="checkbox_icon"></span>
                  <span className="checkbox_text">自动登录</span>
                </label>
              </div>
              <div className="form_checkbox checked">
                <input type="checkbox" id="loginProtocal" defaultChecked />
                <label htmlFor="loginProtocal">
                  <span className="checkbox_icon"></span>
                  <span className="checkbox_text">已阅读并同意</span>
                  <a
                    href="https://privacy.wps.cn/policies/privacy/kdocs"
                    target="_blank"
                    className="protocol_link"
                  >
                    隐私政策
                  </a>
                  <span>和</span>
                  <a
                    href="https://www.kdocs.cn/m/privacy"
                    target="_blank"
                    className="protocol_link"
                  >
                    在线服务协议
                  </a>
                </label>
              </div>
            </div>

            <div className="login_methods">
              <div className="login_method">
                <div className="method_icon qq_icon"></div>
                <span className="method_text">QQ账号</span>
              </div>
              <div className="login_method">
                <div className="method_icon phone_icon"></div>
                <span className="method_text">手机</span>
              </div>
              <div className="login_method">
                <div className="method_icon sso_icon"></div>
                <span className="method_text">专属账号</span>
              </div>
              <div className="login_method">
                <div className="method_icon more_icon"></div>
                <span className="method_text">更多</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home
