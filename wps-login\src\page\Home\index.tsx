import "./index.css"
function Home() {
  return (
    <div className="wrap">
      <div className="logo_wrap">
        <span className="logo" style={{ display: "block" }}></span>
      </div>
      <div className="main">
        <div id="mainWrap">
          <div className="main_item_block">
            <div className="main_item_top">
              <div className="nav">
                <div id="Back" className="nav_back dpn">
                  <i className="icon_nav_back"></i>
                  <span>返回</span>
                </div>
                <div className="nav_title">微信扫码登录</div>
                <div className="nav_intro">使用金山办公在线服务账号登录</div>
              </div>
            </div>
            <div className="code_block"></div>
            <div id="footWrap" className="main_item_bottom">
              <div className="keepOnline_wrap form_checkbox">
                <input
                  type="checkbox"
                  id="keepOnline"
                  className="js_l_checkbox"
                />
                <label htmlFor="keepOnline">
                  <span className="checked_img uncheck f_icon"></span>
                  <span className="checked_img checked f_icon"></span>
                  <span className="keepOnline_txt">自动登录</span>
                </label>
              </div>
              <div className="js_toProtocolCheckbox form_checkbox checked">
                <input
                  type="checkbox"
                  id="loginProtocal"
                  className="js_protocol_checkbox"
                />
                <label htmlFor="loginProtocal">
                  <span className="checked_img uncheck f_icon"></span>
                  <span className="checked_img checked f_icon"></span>
                  <span className="checkbox_txt">已阅读并同意</span>
                  <span id="protocolBlock">
                    <a
                      href="https://privacy.wps.cn/policies/privacy/kdocs"
                      target="_blank"
                      className="js_protocol_url"
                    >
                      隐私政策
                    </a>
                    和
                    <a
                      href="https://www.kdocs.cn/m/privacy"
                      target="_blank"
                      className="js_protocol_url"
                    >
                      在线服务协议
                    </a>
                  </span>
                </label>
              </div>
              <div className="foot_icon_block">
                <a
                  id="qq"
                  data-type="qq"
                  data-dwbtntype="qq_login"
                  className="js_TPLogin"
                >
                  <span className="f_icon icon_round_qq"></span>
                  <span className="f_icon_txt">QQ账号</span>
                </a>
                <a
                  data-to="smsWrap"
                  data-dwbtntype="phonesms_login"
                  data-dwpagetype="phonesms_login"
                  className="js_toProtocolDialog"
                >
                  <span className="f_icon icon_round_phone"></span>
                  <span className="f_icon_txt">手机</span>
                </a>
                <a
                  id="otherLogin"
                  data-to="ssoWrap"
                  data-dwbtntype="sso_login"
                  data-dwpagetype="sso_login"
                  className="js_toProtocolDialog"
                >
                  <span className="f_icon icon_round_sso"></span>
                  <span className="f_icon_txt">专属账号</span>
                </a>
                <a
                  data-to="moreWrap"
                  data-dwbtntype="more"
                  data-dwpagetype="more"
                  className="js_toProtocolDialog more"
                >
                  <span className="f_icon icon_round_login_more"></span>
                  <span className="f_icon_txt">更多</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
export default Home
