/**头标题，尾部考虑为自定义的标题头设置，中间和底部可以传入children */
import React from "react"

interface CommonProps {
  onBack?: () => void
  isShowBack: boolean
  title: string
  subTitle?: string
  children: React.ReactNode
  fchildren?: React.ReactNode
}

function Common(props: CommonProps) {
  const { onBack, isShowBack, title, subTitle, children, fchildren } = props
  return (
    <div className="login">
      {isShowBack && (
        <button className="back_btn" onClick={onBack}>
          ← 返回
        </button>
      )}
      <div className="login_header">
        <div className="nav_title">{title}</div>
        <div className="nav_intro">{subTitle}</div>
      </div>
      <div className="login-child">{children}</div>
      <div className="login-footer">{fchildren}</div>
    </div>
  )
}

export default Common
