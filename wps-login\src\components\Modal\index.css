/* 弹窗样式 */
.modal_overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal_content {
  background: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: modalFadeIn 0.2s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal_header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.modal_close {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.modal_close:hover {
  background: #f5f5f5;
  color: #666;
}

.modal_body {
  padding: 20px 24px;
}

.modal_body p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.modal_footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 20px;
  border-top: 1px solid #f0f0f0;
}

.modal_btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid;
  min-width: 64px;
}

.cancel_btn {
  background: white;
  color: #666;
  border-color: #d9d9d9;
}

.cancel_btn:hover {
  background: #f5f5f5;
  border-color: #b3b3b3;
}

.confirm_btn {
  background: #4285f4;
  color: white;
  border-color: #4285f4;
}

.confirm_btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal_content {
    margin: 20px;
    width: auto;
  }
  
  .modal_header,
  .modal_body,
  .modal_footer {
    padding-left: 16px;
    padding-right: 16px;
  }
}
