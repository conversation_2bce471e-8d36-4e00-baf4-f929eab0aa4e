import Common from "../Common"
import "./index.css"

interface QRCodeLoginProps {
  onPhoneClick: () => void
  onSSOClick: () => void
}

function QRCodeLogin({ onPhoneClick, onSSOClick }: QRCodeLoginProps) {
  return (
    <Common
      title="微信扫码登录"
      subTitle="使用金山办公在线服务账号登录"
      fchildren={
        <div className="login_options">
          <div className="checkbox_section">
            <div className="form_checkbox">
              <input type="checkbox" id="keepOnline" />
              <label htmlFor="keepOnline">
                <span className="checkbox_icon"></span>
                <span className="checkbox_text">自动登录</span>
              </label>
            </div>
            <div className="form_checkbox checked">
              <input type="checkbox" id="loginProtocal" defaultChecked />
              <label htmlFor="loginProtocal">
                <span className="checkbox_icon"></span>
                <span className="checkbox_text">已阅读并同意</span>
                <a
                  href="https://privacy.wps.cn/policies/privacy/kdocs"
                  target="_blank"
                  className="protocol_link"
                >
                  隐私政策
                </a>
                <span>和</span>
                <a
                  href="https://www.kdocs.cn/m/privacy"
                  target="_blank"
                  className="protocol_link"
                >
                  在线服务协议
                </a>
              </label>
            </div>
          </div>

          <div className="login_methods">
            <div className="login_method">
              <div className="method_icon qq_icon">
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="2768"
                  id="mx_n_1753146181203"
                  width="32"
                  height="32"
                >
                  <path
                    d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m210.5 612.4c-11.5 1.4-44.9-52.7-44.9-52.7 0 31.3-16.2 72.2-51.1 101.8 16.9 5.2 54.9 19.2 45.9 34.4-7.3 12.3-125.6 7.9-159.8 4-34.2 3.8-152.5 8.3-159.8-4-9.1-15.2 28.9-29.2 45.8-34.4-35-29.5-51.1-70.4-51.1-101.8 0 0-33.4 54.1-44.9 52.7-5.4-0.7-12.4-29.6 9.4-99.7 10.3-33 22-60.5 40.2-105.8-3.1-116.9 45.3-215 160.4-215 113.9 0 163.3 96.1 160.4 215 18.1 45.2 29.9 72.8 40.2 105.8 21.7 70.1 14.6 99.1 9.3 99.7z"
                    p-id="2769"
                    fill="#1195db"
                  ></path>
                </svg>
              </div>
              <span className="method_text">QQ账号</span>
            </div>
            <div className="login_method" onClick={onPhoneClick}>
              <div className="method_icon phone_icon">
                <svg
                  className="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="51375"
                  width="32"
                  height="32"
                >
                  <path
                    d="M512 0c282.771911 0 512 229.228089 512 512s-229.228089 512-512 512S0 794.771911 0 512 229.228089 0 512 0z m111.189333 204.714667h-222.378666c-11.275378 0-21.822578 2.133333-31.635911 6.405689a81.095111 81.095111 0 0 0-25.685334 17.544533 82.147556 82.147556 0 0 0-17.225955 25.969778c-4.175644 9.898667-6.263467 20.462933-6.263467 31.704177v451.9936c0 11.246933 2.087822 21.703111 6.263467 31.368534a83.228444 83.228444 0 0 0 17.225955 25.634133 81.095111 81.095111 0 0 0 25.685334 17.544533c9.813333 4.266667 20.360533 6.405689 31.630222 6.405689h222.384355c11.275378 0 21.822578-2.133333 31.635911-6.405689a81.095111 81.095111 0 0 0 25.685334-17.544533 83.228444 83.228444 0 0 0 17.225955-25.634133c4.175644-9.671111 6.263467-20.1216 6.263467-31.368534V286.338844c0-11.241244-2.087822-21.8112-6.263467-31.704177a82.147556 82.147556 0 0 0-17.225955-25.969778 81.095111 81.095111 0 0 0-25.685334-17.544533c-9.813333-4.266667-20.360533-6.405689-31.630222-6.405689z m-111.502222 519.452444c9.187556 0 16.913067 3.373511 23.176533 10.120533 6.263467 6.741333 9.398044 15.064178 9.398045 24.957156 0 9.443556-3.128889 17.652622-9.398045 24.6272-6.263467 6.968889-13.988978 10.456178-23.176533 10.456178-8.772267 0-16.389689-3.487289-22.869333-10.456178-6.468267-6.974578-9.705244-15.183644-9.705245-24.6272 0-9.892978 3.236978-18.215822 9.710934-24.957156 6.473956-6.747022 14.091378-10.126222 22.863644-10.126222z m141.573689-405.447111v386.56H370.113422v-386.56h283.147378zM551.776711 249.912889c2.503111 0 4.801422 1.012622 6.894933 3.037867 2.087822 2.019556 3.128889 4.835556 3.128889 8.430933s-1.041067 6.405689-3.128889 8.430933c-2.093511 2.025244-4.386133 3.037867-6.894933 3.037867H472.223289c-2.503111 0-4.801422-1.012622-6.894933-3.037867-2.087822-2.025244-3.128889-4.835556-3.128889-8.430933 0-3.601067 1.149156-6.411378 3.447466-8.430933 2.292622-2.025244 4.488533-3.037867 6.576356-3.037867z"
                    fill="#11a7fa"
                    p-id="51376"
                  ></path>
                </svg>
              </div>
              <span className="method_text">手机</span>
            </div>
            <div className="login_method" onClick={onSSOClick}>
              <div className="method_icon sso_icon"></div>
              <span className="method_text">专属账号</span>
            </div>
            <div className="login_method">
              <div className="method_icon more_icon"></div>
              <span className="method_text">更多</span>
            </div>
          </div>
        </div>
      }
    >
      <div className="qr_code_section">
        <div className="qr_code_container">
          <div className="qr_code_placeholder"></div>
        </div>
      </div>
    </Common>
  )
}

export default QRCodeLogin
