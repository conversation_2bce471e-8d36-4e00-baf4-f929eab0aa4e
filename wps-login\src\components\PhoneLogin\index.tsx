import { useState } from "react"
import Common from "../Common"
import "./index.css"

interface PhoneLoginProps {
  onBack: () => void
}

function PhoneLogin({ onBack }: PhoneLoginProps) {
  const [isVerified, setIsVerified] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSmartVerify = () => {
    setIsLoading(true)
    // 模拟智能验证过程
    setTimeout(() => {
      setIsVerified(true)
      setIsLoading(false)
    }, 1000)
  }

  const handleSendCode = () => {
    if (!phoneNumber.trim()) {
      alert("请输入手机号码")
      return
    }

    console.log("发送验证码到:", phoneNumber)
    alert("验证码已发送")
  }

  const handleLogin = () => {
    if (!phoneNumber.trim()) {
      alert("请输入手机号码")
      return
    }
    if (!verificationCode.trim()) {
      alert("请输入验证码")
      return
    }

    console.log("登录信息:", { phoneNumber, verificationCode })
    alert("登录成功")
  }

  return (
    <Common
      isShowBack={true}
      onBack={onBack}
      title="短信验证码登录"
      subTitle="使用金山办公在线服务账号登录"
      fchildren={
        <div className="login_footer">
          <p>未注册的手机号验证后将自动创建金山账号</p>
        </div>
      }
    >
      <div className="phone_login_section">
        <div className="phone_input_group">
          <div className="country_code_select">
            <select className="country_code">
              <option value="+86">+86</option>
            </select>
          </div>
          <input
            type="tel"
            className="phone_input"
            placeholder="手机号码"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
          />
        </div>

        {!isVerified ? (
          <div className="smart_verify_section">
            <div className="verify_button_container">
              <button
                className="smart_verify_btn"
                onClick={handleSmartVerify}
                disabled={isLoading}
              >
                <span className="verify_icon">○</span>
                <span className="verify_text">
                  {isLoading ? "验证中..." : "点击按钮开始智能验证"}
                </span>
              </button>
            </div>
          </div>
        ) : (
          <div className="verification_group">
            <input
              type="text"
              className="verification_input"
              placeholder="短信验证码"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
            />
            <button className="send_code_btn" onClick={handleSendCode}>
              发送验证码
            </button>
          </div>
        )}
        <button className="login_submit_btn" onClick={handleLogin}>
          立即登录/注册
        </button>
      </div>
    </Common>
  )
}

export default PhoneLogin
