.wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #1E2023;
}

.logo_wrap {
  width: 400px;
  height: 500px;
  margin-right: 10%;
  overflow: hidden;
}

.logo {
  display: block;
  /* 或者根据需要用条件渲染来控制 */
  width: 400px;
  height: 500px;
  background-image: url(https://ac.wpscdn.cn/account/libs/img/v2/logo/logo_x2.89795d69.png);
  background-size: contain;
  /* 保证图片保持比例 */
  background-repeat: no-repeat;
}

.main {
  position: relative;
  display: inline-block;
  overflow: hidden;
  width: 398px;
  text-align: center;
  background: #FFF;

  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, .05);
  border-radius: 6px;
  border: 1px solid #E7E9EB;
}

.main_item_top {
  width: 100%;
  margin: auto;
}

.nav_back {
  position: absolute;
  top: 26px;
  left: 29px;
  color: #417FF9;
  text-align: left;
  cursor: pointer;
  z-index: 1;
}

.dpn {
  display: none;
}

.icon_nav_back {
  background-image: url(https://ac.wpscdn.cn/account/libs/img/v2/nav_back_x2.44eaffe6.png);
  background-size: 20px;
}

.nav_title {
  font-size: 26px;
  line-height: 38px;
  color: #1E2023;
}

.nav_intro {
  line-height: 20px;
  padding-top: 4px;
  color: #737579;
}

.code_block {
  flex: auto;
}

.main_item_bottom {
  width: 100%;
}