/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #ffffff;
}

/* 主容器 */
.wrap {
  display: flex;
  min-height: 100vh;
  background-color: #ffffff;
  align-items: center;
  justify-content: space-between;
  padding: 40px 100px;
  max-width: 1400px;
  margin: 0 auto;
  gap: 80px;
}

/* 左侧区域 */
.left_section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
}

.logo_wrap {
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo_image {
  max-width: 400px;
  max-height: 500px;
  width: auto;
  height: auto;
  object-fit: contain;
}

/* 右侧区域 */
.right_section {
  width: 400px;
  flex-shrink: 0;
  background-color: #fff;
  padding: 32px;
  border: 1px solid #e7e9eb;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.login_container {
  width: 100%;
  max-width: 400px;
  text-align: center;
}

/* 登录头部 */
.login_header {
  margin-bottom: 32px;
}

.nav_title {
  font-size: 22px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
}

.nav_intro {
  font-size: 13px;
  color: #999;
  line-height: 1.4;
  text-align: center;
}

/* 二维码区域 */
.qr_code_section {
  margin-bottom: 35px;
}

.qr_code_container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.qr_code_placeholder {
  width: 180px;
  height: 180px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-image: url('https://qrcode.qwps.cn/wxmp/minicodes/wxDiVkbXr0fgABqQ77?Expires=1753115331&response-content-type=image%2Fjpeg&AWSAccessKeyId=AKLTbmLdUXBURTS90LsAeTxk&Signature=L3N0KleglY8qm4purVNI1LOtYwc%3D');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 登录选项区域 */
.login_options {
  text-align: left;
}

/* 复选框区域 */
.checkbox_section {
  margin-bottom: 30px;
}

.form_checkbox {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.form_checkbox input[type="checkbox"] {
  display: none;
}

.form_checkbox label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.checkbox_icon {
  width: 14px;
  height: 14px;
  border: 1px solid #ddd;
  border-radius: 2px;
  margin-right: 8px;
  margin-top: 1px;
  flex-shrink: 0;
  position: relative;
  background-color: #fff;
}

.form_checkbox.checked .checkbox_icon::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: #00d100;
  font-size: 12px;
  font-weight: bold;
}

.checkbox_text {
  margin-right: 4px;
}

.protocol_link {
  color: #4285f4;
  text-decoration: none;
  margin: 0 2px;
}

.protocol_link:hover {
  text-decoration: underline;
}

/* 登录方式区域 */
.login_methods {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 10px;
}

.login_method {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  padding: 8px;
  border-radius: 8px;
}

.login_method:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
}

.method_icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-bottom: 6px;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  background-position: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qq_icon {
  background-color: #12b7f5;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTYuNDE4IDIgMjAgNS41ODIgMjAgMTBDMjAgMTQuNDE4IDE2LjQxOCAxOCAxMiAxOEM3LjU4MiAxOCA0IDE0LjQxOCA0IDEwQzQgNS41ODIgNy41ODIgMiAxMiAyWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==');
}

.phone_icon {
  background-color: #ff6b35;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYuNjIgMTAuNzlDOC4wNiAxMy42MiAxMC4zOCAxNS45MyAxMy4yMSAxNy4zN0wxNS40MSAxNS4xOEMxNS42OSAxNC45IDE2LjA4IDE0LjgyIDE2LjQzIDE0LjkzQzE3LjU1IDE1LjMgMTguNzUgMTUuNSAyMCAxNS41QzIwLjU1IDE1LjUgMjEgMTUuOTUgMjEgMTYuNVYyMEMyMSAyMC41NSAyMC41NSAyMSAyMCAyMUMxMC42MSAyMSAzIDEzLjM5IDMgNEMzIDMuNDUgMy40NSAzIDQgM0g3LjVDOC4wNSAzIDguNSAzLjQ1IDguNSA0QzguNSA1LjI1IDguNyA2LjQ1IDkuMDcgNy41N0M5LjE4IDcuOTIgOS4xIDguMzEgOC44MiA4LjU5TDYuNjIgMTAuNzlaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');
}

.sso_icon {
  background-color: #9c27b0;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');
}

.more_icon {
  background-color: #757575;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIyIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIyIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIxOSIgY3k9IjEyIiByPSIyIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');
}

.method_text {
  font-size: 11px;
  color: #666;
  text-align: center;
  font-weight: 400;
  white-space: nowrap;
}

/* 弹窗样式 */
.modal_overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal_content {
  background: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90vw;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal_title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.modal_close {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.modal_close:hover {
  background-color: #f5f5f5;
  color: #666;
}

.modal_body {
  padding: 20px 24px;
}

.modal_body p {
  margin: 0;
  line-height: 1.6;
  color: #666;
  font-size: 14px;
}

.modal_footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 20px;
  border-top: 1px solid #f0f0f0;
}

.modal_btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  min-width: 64px;
}

.cancel_btn {
  background: white;
  color: #666;
  border-color: #d9d9d9;
}

.cancel_btn:hover {
  background: #f5f5f5;
  border-color: #b3b3b3;
}

.confirm_btn {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.confirm_btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 手机验证码登录样式 */
.back_btn {
  background: none;
  border: none;
  color: #4285f4;
  font-size: 14px;
  cursor: pointer;
  padding: 0;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  transition: color 0.2s;
  text-decoration: none;
  align-self: flex-start;
  position: relative;
  left: -8px;
}

.back_btn:hover {
  color: #1976d2;
  text-decoration: underline;
}

.phone_login_section {
  padding: 20px 0;
}

.phone_input_group {
  display: flex;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
  background: white;
  height: 48px;
}

.country_code_select {
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  position: relative;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.country_code {
  background: transparent;
  border: none;
  padding: 0 8px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  outline: none;
  appearance: none;
  text-align: center;
  width: 100%;
}

.country_code_select::after {
  content: '▼';
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: #999;
  pointer-events: none;
}

.phone_input {
  flex: 1;
  border: none;
  padding: 0 16px;
  font-size: 14px;
  outline: none;
  background: transparent;
  height: 100%;
}

.phone_input::placeholder {
  color: #999;
}

.phone_input:focus,
.phone_input_group:focus-within {
  border-color: #4285f4;
}

/* 智能验证按钮样式 */
.smart_verify_section {
  margin-bottom: 20px;
}

.verify_button_container {
  display: flex;
  justify-content: center;
}

.smart_verify_btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: #333;
  min-width: 200px;
  height: 48px;
}

.smart_verify_btn:hover {
  border-color: #4285f4;
  background: #f8f9ff;
}

.verify_icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #00d4aa;
  border-radius: 50%;
  margin-right: 8px;
  position: relative;
  background: #00d4aa;
}

.verify_icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

.verify_text {
  color: #333;
  font-size: 14px;
}

.verification_group {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.verification_input {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px 16px;
  font-size: 14px;
  outline: none;
  background: white;
  height: 48px;
  box-sizing: border-box;
}

.verification_input::placeholder {
  color: #999;
}

.verification_input:focus {
  border-color: #4285f4;
}

.send_code_btn {
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0 20px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.2s;
  min-width: 140px;
  height: 48px;
}

.send_code_btn:hover {
  background: #1976d2;
}

.verification_tip {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 4px 0;
}

.tip_icon {
  color: #00d4aa;
  font-weight: bold;
  margin-right: 8px;
  font-size: 14px;
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00d4aa;
  color: white;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
}

.tip_text {
  color: #666;
  font-size: 13px;
}

.login_submit_btn {
  width: 100%;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 14px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 20px;
  transition: background-color 0.2s;
  height: 48px;
  margin-top: 10px;
}

.login_submit_btn:hover {
  background: #1976d2;
}

.phone_login_footer {
  text-align: center;
  padding-top: 10px;
}

.phone_login_footer p {
  font-size: 12px;
  color: #999;
  margin: 0;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .wrap {
    padding: 40px 60px;
    gap: 60px;
  }
}

@media (max-width: 768px) {
  .wrap {
    flex-direction: column;
    padding: 20px;
    gap: 40px;
    justify-content: center;
  }

  .left_section {
    justify-content: center;
  }

  .logo_image {
    max-width: 300px;
    max-height: 350px;
  }

  .right_section {
    width: 100%;
    max-width: 400px;
    padding: 30px 20px;
    margin: 0 auto;
  }

  .login_methods {
    gap: 15px;
  }

  .modal_content {
    width: 320px;
    margin: 20px;
  }

  .modal_header,
  .modal_body,
  .modal_footer {
    padding-left: 16px;
    padding-right: 16px;
  }

  .verification_group {
    flex-direction: column;
    gap: 16px;
  }

  .send_code_btn {
    width: 100%;
  }
}