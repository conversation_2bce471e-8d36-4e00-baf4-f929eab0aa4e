/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #f5f5f5;
}

/* 主容器 */
.wrap {
  display: flex;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 左侧区域 */
.left_section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  padding: 40px;
}

.logo_wrap {
  text-align: center;
}

.logo {
  width: 80px;
  height: 80px;
  background-color: #ff3333;
  margin: 0 auto 30px;
  position: relative;
  border-radius: 8px;
}

.logo::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 20px;
  background-color: white;
  border-radius: 2px;
}

.logo_text {
  text-align: left;
}

.company_name {
  font-size: 42px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  letter-spacing: 3px;
}

.company_name_en {
  font-size: 16px;
  color: #666;
  margin-bottom: 40px;
  letter-spacing: 2px;
  font-weight: 400;
}

.slogan {
  font-size: 18px;
  color: #333;
  margin-bottom: 6px;
  letter-spacing: 2px;
  font-weight: 400;
}

.slogan_en {
  font-size: 14px;
  color: #666;
  font-style: italic;
  letter-spacing: 1px;
}

/* 右侧区域 */
.right_section {
  width: 420px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  padding: 60px 40px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05);
}

.login_container {
  width: 100%;
  max-width: 400px;
  text-align: center;
}

/* 登录头部 */
.login_header {
  margin-bottom: 35px;
}

.nav_title {
  font-size: 22px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
}

.nav_intro {
  font-size: 13px;
  color: #999;
  line-height: 1.4;
}

/* 二维码区域 */
.qr_code_section {
  margin-bottom: 35px;
}

.qr_code_container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
}

.qr_code_placeholder {
  width: 180px;
  height: 180px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-image:
    /* 模拟二维码图案 */
    linear-gradient(90deg, #000 0%, #000 10%, transparent 10%, transparent 20%, #000 20%, #000 30%, transparent 30%, transparent 40%, #000 40%, #000 50%, transparent 50%, transparent 60%, #000 60%, #000 70%, transparent 70%, transparent 80%, #000 80%, #000 90%, transparent 90%),
    linear-gradient(0deg, #000 0%, #000 10%, transparent 10%, transparent 20%, #000 20%, #000 30%, transparent 30%, transparent 40%, #000 40%, #000 50%, transparent 50%, transparent 60%, #000 60%, #000 70%, transparent 70%, transparent 80%, #000 80%, #000 90%, transparent 90%);
  background-size: 20px 20px, 20px 20px;
  background-position: 0 0, 0 0;
}

.qr_code_placeholder::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: #00d100;
  border-radius: 50%;
  z-index: 1;
}

.qr_code_placeholder::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background-color: white;
  border-radius: 50%;
  z-index: 2;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjMDBEMTAwIi8+Cjwvc3ZnPgo=');
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
}

/* 登录选项区域 */
.login_options {
  text-align: left;
}

/* 复选框区域 */
.checkbox_section {
  margin-bottom: 30px;
}

.form_checkbox {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.form_checkbox input[type="checkbox"] {
  display: none;
}

.form_checkbox label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.checkbox_icon {
  width: 14px;
  height: 14px;
  border: 1px solid #ddd;
  border-radius: 2px;
  margin-right: 8px;
  margin-top: 1px;
  flex-shrink: 0;
  position: relative;
  background-color: #fff;
}

.form_checkbox.checked .checkbox_icon::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: #00d100;
  font-size: 12px;
  font-weight: bold;
}

.checkbox_text {
  margin-right: 4px;
}

.protocol_link {
  color: #4285f4;
  text-decoration: none;
  margin: 0 2px;
}

.protocol_link:hover {
  text-decoration: underline;
}

/* 登录方式区域 */
.login_methods {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 10px;
}

.login_method {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  padding: 8px;
  border-radius: 8px;
}

.login_method:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
}

.method_icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-bottom: 6px;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  background-position: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qq_icon {
  background-color: #12b7f5;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTYuNDE4IDIgMjAgNS41ODIgMjAgMTBDMjAgMTQuNDE4IDE2LjQxOCAxOCAxMiAxOEM3LjU4MiAxOCA0IDE0LjQxOCA0IDEwQzQgNS41ODIgNy41ODIgMiAxMiAyWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==');
}

.phone_icon {
  background-color: #ff6b35;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTYuNjIgMTAuNzlDOC4wNiAxMy42MiAxMC4zOCAxNS45MyAxMy4yMSAxNy4zN0wxNS40MSAxNS4xOEMxNS42OSAxNC45IDE2LjA4IDE0LjgyIDE2LjQzIDE0LjkzQzE3LjU1IDE1LjMgMTguNzUgMTUuNSAyMCAxNS41QzIwLjU1IDE1LjUgMjEgMTUuOTUgMjEgMTYuNVYyMEMyMSAyMC41NSAyMC41NSAyMSAyMCAyMUMxMC42MSAyMSAzIDEzLjM5IDMgNEMzIDMuNDUgMy40NSAzIDQgM0g3LjVDOC4wNSAzIDguNSAzLjQ1IDguNSA0QzguNSA1LjI1IDguNyA2LjQ1IDkuMDcgNy41N0M5LjE4IDcuOTIgOS4xIDguMzEgOC44MiA4LjU5TDYuNjIgMTAuNzlaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');
}

.sso_icon {
  background-color: #9c27b0;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');
}

.more_icon {
  background-color: #757575;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIyIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIyIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIxOSIgY3k9IjEyIiByPSIyIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');
}

.method_text {
  font-size: 11px;
  color: #666;
  text-align: center;
  font-weight: 400;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wrap {
    flex-direction: column;
  }

  .left_section {
    padding: 20px;
  }

  .right_section {
    width: 100%;
    padding: 20px;
  }

  .company_name {
    font-size: 32px;
  }

  .login_methods {
    gap: 15px;
  }
}