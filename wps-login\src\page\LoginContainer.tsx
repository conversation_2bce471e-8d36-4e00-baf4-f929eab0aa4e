// pages/LoginContainer.tsx
import React, { useState } from "react"
import { LoginOptions } from "./LoginOptions"
import { PhoneLogin } from "./PhoneLogin"
import { QQLogin } from "./QQLogin"
import { WechatLogin } from "./WechatLogin"
type View = "options" | "wechat" | "phone" | "qq"

const LoginContainer: React.FC = () => {
  const [view, setView] = useState<View>("options")

  const handleBack = () => {
    setView("options")
  }

  return (
    <div className="w-[400px] border rounded shadow-lg p-6 mx-auto mt-20">
      {/* 标题区域 */}
      <h2 className="text-xl font-bold text-center mb-4">登录金山服务</h2>

      {/* 切换区域 */}
      {view !== "options" && (
        <button
          onClick={handleBack}
          className="mb-4 text-blue-500 hover:underline"
        >
          ← 返回
        </button>
      )}

      {/* 内容区域 */}
      <div>
        {view === "options" && <LoginOptions onSelect={setView} />}
        {view === "wechat" && <WechatLogin />}
        {view === "phone" && <PhoneLogin />}
        {view === "qq" && <QQLogin />}
      </div>
    </div>
  )
}

export default LoginContainer
