import "./index.css"

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
}

function Modal({ isOpen, onClose, onConfirm }: ModalProps) {
  if (!isOpen) return null

  return (
    <div className="modal_overlay" onClick={onClose}>
      <div className="modal_content" onClick={(e) => e.stopPropagation()}>
        <div className="modal_header">
          <h3>提示</h3>
          <button className="modal_close" onClick={onClose}>
            ×
          </button>
        </div>
        <div className="modal_body">
          <p>
            欢迎选择使用金山办公在线服务!在您使用本服务前，请您务必充分阅读
            <a href="#">隐私政策</a>和<a href="#">在线服务协议</a>
            ，我们将严格按照协议内容您提供服务、保护您的个人信息。
          </p>
        </div>
        <div className="modal_footer">
          <button className="modal_btn cancel_btn" onClick={onClose}>
            取消
          </button>
          <button className="modal_btn confirm_btn" onClick={onConfirm}>
            确定
          </button>
        </div>
      </div>
    </div>
  )
}

export default Modal
