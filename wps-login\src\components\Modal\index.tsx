import "./index.css"

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
}

function Modal({ isOpen, onClose, onConfirm }: ModalProps) {
  if (!isOpen) return null

  return (
    <div className="modal_overlay" onClick={onClose}>
      <div className="modal_content" onClick={(e) => e.stopPropagation()}>
        <div className="modal_header">
          <h3>提示</h3>
          <button className="modal_close" onClick={onClose}>
            ×
          </button>
        </div>
        <div className="modal_body">
          <p>
            此功能暂未开放，请使用微信扫码登录。点击下方"确定"按钮，或者点击"取消"按钮返回。
          </p>
        </div>
        <div className="modal_footer">
          <button className="modal_btn cancel_btn" onClick={onClose}>
            取消
          </button>
          <button className="modal_btn confirm_btn" onClick={onConfirm}>
            确定
          </button>
        </div>
      </div>
    </div>
  )
}

export default Modal
