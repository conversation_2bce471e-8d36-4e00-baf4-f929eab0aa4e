/* 二维码区域 */
.qr_code_section {
  margin-bottom: 35px;
}

.qr_code_container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 200px;

}

.qr_code_placeholder {
  width: 160px;
  height: 160px;
  position: relative;
}



/* 登录选项 */
.login_options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 复选框区域 */
.checkbox_section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form_checkbox {
  display: flex;
  align-items: center;
}

.form_checkbox input[type="checkbox"] {
  display: none;
}

.form_checkbox label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.checkbox_icon {
  width: 16px;
  height: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  margin-right: 8px;
  position: relative;
  background: white;
  flex-shrink: 0;
}

.form_checkbox.checked .checkbox_icon,
.form_checkbox input[type="checkbox"]:checked+label .checkbox_icon {
  background: #4285f4;
  border-color: #4285f4;
}

.form_checkbox.checked .checkbox_icon::after,
.form_checkbox input[type="checkbox"]:checked+label .checkbox_icon::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.checkbox_text {
  margin-right: 4px;
}

.protocol_link {
  color: #4285f4;
  text-decoration: none;
  margin: 0 2px;
}

.protocol_link:hover {
  text-decoration: underline;
}

/* 登录方式 */
.login_methods {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.login_method {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login_method:hover {
  background: #f8f9fa;
}

.method_icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}




.sso_icon {
  background: #722ed1;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.more_icon {
  background: #8c8c8c;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M6 10c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm12 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm-6 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z'/%3E%3C/svg%3E");
}

.method_text {
  font-size: 12px;
  color: #666;
  text-align: center;
}