// LoginOptions.tsx
import React from "react"

interface Props {
  onSelect: (view: "wechat" | "phone" | "qq") => void
}

export const LoginOptions: React.FC<Props> = ({ onSelect }) => {
  return (
    <div className="flex flex-col gap-4">
      <button onClick={() => onSelect("wechat")} className="btn">
        微信扫码登录
      </button>
      <button onClick={() => onSelect("phone")} className="btn">
        手机号登录
      </button>
      <button onClick={() => onSelect("qq")} className="btn">
        QQ账号登录
      </button>
    </div>
  )
}
